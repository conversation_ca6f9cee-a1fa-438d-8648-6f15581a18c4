#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة إضافة الميزات الجديدة لنظام نقاط البيع
New Features Addition Tool for POS System
"""

import os
import sqlite3
from datetime import datetime

class FeatureManager:
    """مدير الميزات الجديدة"""
    
    def __init__(self):
        self.db_path = "pos_database.db"
        
    def get_connection(self):
        """الحصول على اتصال قاعدة البيانات"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def add_barcode_support(self):
        """إضافة دعم الباركود المحسن"""
        print("🔧 إضافة دعم الباركود المحسن...")
        
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # التحقق من وجود عمود الباركود
        cursor.execute("PRAGMA table_info(products)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'barcode' not in columns:
            cursor.execute('ALTER TABLE products ADD COLUMN barcode TEXT')
            print("   ✅ تم إضافة عمود الباركود")
        
        # إنشاء فهرس للباركود
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_products_barcode ON products(barcode)')
        
        conn.commit()
        conn.close()
        print("   ✅ تم تحسين دعم الباركود")
    
    def add_advanced_reports(self):
        """إضافة تقارير متقدمة"""
        print("🔧 إضافة جداول التقارير المتقدمة...")
        
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # جدول تقارير المبيعات اليومية
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS daily_sales_reports (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                report_date TEXT NOT NULL,
                total_sales REAL DEFAULT 0,
                total_invoices INTEGER DEFAULT 0,
                total_items_sold INTEGER DEFAULT 0,
                cash_sales REAL DEFAULT 0,
                credit_sales REAL DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول تقارير المخزون
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS inventory_reports (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                report_date TEXT NOT NULL,
                product_id INTEGER,
                product_name TEXT,
                opening_stock INTEGER DEFAULT 0,
                purchases INTEGER DEFAULT 0,
                sales INTEGER DEFAULT 0,
                closing_stock INTEGER DEFAULT 0,
                stock_value REAL DEFAULT 0,
                FOREIGN KEY (product_id) REFERENCES products (id)
            )
        ''')
        
        conn.commit()
        conn.close()
        print("   ✅ تم إضافة جداول التقارير المتقدمة")
    
    def add_customer_credit_system(self):
        """إضافة نظام الائتمان للعملاء"""
        print("🔧 إضافة نظام الائتمان للعملاء...")
        
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # التحقق من وجود أعمدة الائتمان
        cursor.execute("PRAGMA table_info(customers)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'credit_limit' not in columns:
            cursor.execute('ALTER TABLE customers ADD COLUMN credit_limit REAL DEFAULT 0')
            print("   ✅ تم إضافة عمود حد الائتمان")
        
        if 'current_balance' not in columns:
            cursor.execute('ALTER TABLE customers ADD COLUMN current_balance REAL DEFAULT 0')
            print("   ✅ تم إضافة عمود الرصيد الحالي")
        
        # جدول حركات الائتمان
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS customer_credit_movements (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                customer_id INTEGER NOT NULL,
                movement_type TEXT NOT NULL, -- debit, credit, payment
                amount REAL NOT NULL,
                description TEXT,
                reference_type TEXT, -- invoice, payment, adjustment
                reference_id INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES customers (id)
            )
        ''')
        
        conn.commit()
        conn.close()
        print("   ✅ تم إضافة نظام الائتمان للعملاء")
    
    def add_product_categories(self):
        """إضافة تصنيفات المنتجات المحسنة"""
        print("🔧 تحسين نظام تصنيفات المنتجات...")
        
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # التحقق من وجود عمود التصنيف
        cursor.execute("PRAGMA table_info(products)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'category_id' not in columns:
            cursor.execute('ALTER TABLE products ADD COLUMN category_id INTEGER')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_products_category ON products(category_id)')
            print("   ✅ تم إضافة ربط المنتجات بالتصنيفات")
        
        # إضافة تصنيفات افتراضية
        default_categories = [
            ('مواد غذائية', 'المواد الغذائية والمشروبات'),
            ('مستلزمات منزلية', 'الأدوات والمستلزمات المنزلية'),
            ('ملابس', 'الملابس والأزياء'),
            ('إلكترونيات', 'الأجهزة الإلكترونية'),
            ('أدوات مكتبية', 'القرطاسية والأدوات المكتبية')
        ]
        
        for name, description in default_categories:
            cursor.execute('''
                INSERT OR IGNORE INTO categories (name, description)
                VALUES (?, ?)
            ''', (name, description))
        
        conn.commit()
        conn.close()
        print("   ✅ تم تحسين نظام التصنيفات")
    
    def add_supplier_management(self):
        """تحسين إدارة الموردين"""
        print("🔧 تحسين إدارة الموردين...")
        
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # التحقق من وجود أعمدة إضافية للموردين
        cursor.execute("PRAGMA table_info(suppliers)")
        columns = [column[1] for column in cursor.fetchall()]
        
        new_columns = [
            ('tax_number', 'TEXT'),
            ('payment_terms', 'TEXT'),
            ('credit_limit', 'REAL DEFAULT 0'),
            ('current_balance', 'REAL DEFAULT 0')
        ]
        
        for column_name, column_type in new_columns:
            if column_name not in columns:
                cursor.execute(f'ALTER TABLE suppliers ADD COLUMN {column_name} {column_type}')
                print(f"   ✅ تم إضافة عمود {column_name}")
        
        # جدول حركات الموردين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS supplier_movements (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                supplier_id INTEGER NOT NULL,
                movement_type TEXT NOT NULL, -- purchase, payment, return
                amount REAL NOT NULL,
                description TEXT,
                reference_type TEXT, -- invoice, payment, return
                reference_id INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (supplier_id) REFERENCES suppliers (id)
            )
        ''')
        
        conn.commit()
        conn.close()
        print("   ✅ تم تحسين إدارة الموردين")
    
    def add_notification_system(self):
        """إضافة نظام الإشعارات"""
        print("🔧 إضافة نظام الإشعارات...")
        
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # جدول الإشعارات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS notifications (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                message TEXT NOT NULL,
                notification_type TEXT NOT NULL, -- info, warning, error, success
                priority INTEGER DEFAULT 1, -- 1=low, 2=medium, 3=high
                is_read BOOLEAN DEFAULT 0,
                user_id INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # إضافة إشعارات افتراضية
        default_notifications = [
            ('مرحباً بك', 'تم تشغيل النظام بنجاح', 'success', 1),
            ('تحديث النظام', 'تم إضافة ميزات جديدة للنظام', 'info', 2),
            ('تنبيه المخزون', 'يوجد منتجات قاربت على النفاد', 'warning', 3)
        ]
        
        for title, message, notif_type, priority in default_notifications:
            cursor.execute('''
                INSERT OR IGNORE INTO notifications (title, message, notification_type, priority)
                VALUES (?, ?, ?, ?)
            ''', (title, message, notif_type, priority))
        
        conn.commit()
        conn.close()
        print("   ✅ تم إضافة نظام الإشعارات")
    
    def add_backup_system(self):
        """تحسين نظام النسخ الاحتياطي"""
        print("🔧 تحسين نظام النسخ الاحتياطي...")
        
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # جدول سجل النسخ الاحتياطي
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS backup_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                backup_type TEXT NOT NULL, -- manual, automatic
                backup_path TEXT NOT NULL,
                backup_size INTEGER,
                status TEXT NOT NULL, -- success, failed
                error_message TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                user_id INTEGER,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        conn.commit()
        conn.close()
        print("   ✅ تم تحسين نظام النسخ الاحتياطي")
    
    def add_all_features(self):
        """إضافة جميع الميزات"""
        print("🚀 بدء إضافة جميع الميزات الجديدة...")
        print("=" * 60)
        
        try:
            self.add_barcode_support()
            self.add_advanced_reports()
            self.add_customer_credit_system()
            self.add_product_categories()
            self.add_supplier_management()
            self.add_notification_system()
            self.add_backup_system()
            
            print("=" * 60)
            print("🎉 تم إضافة جميع الميزات بنجاح!")
            print("\n📋 الميزات المضافة:")
            print("   ✅ دعم الباركود المحسن")
            print("   ✅ تقارير متقدمة")
            print("   ✅ نظام ائتمان العملاء")
            print("   ✅ تصنيفات المنتجات المحسنة")
            print("   ✅ إدارة الموردين المحسنة")
            print("   ✅ نظام الإشعارات")
            print("   ✅ نظام النسخ الاحتياطي المحسن")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إضافة الميزات: {str(e)}")
            return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🔧 أداة إضافة الميزات الجديدة")
    print("=" * 60)
    
    feature_manager = FeatureManager()
    
    if feature_manager.add_all_features():
        print("\n🎯 النظام جاهز مع الميزات الجديدة!")
        print("\n📋 لتجربة الميزات الجديدة:")
        print("   1. أعد تشغيل التطبيق")
        print("   2. تحقق من الإشعارات الجديدة")
        print("   3. جرب البحث بالباركود")
        print("   4. استكشف التقارير المتقدمة")
    else:
        print("\n⚠️ حدثت مشاكل أثناء إضافة الميزات")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
