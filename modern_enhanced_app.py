#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
التطبيق المحسن - نظام نقاط البيع والمحاسبة
Enhanced Application - POS and Accounting System

الميزات الجديدة:
- نظام الإشعارات المتقدم
- التقارير المتقدمة
- البحث المحسن بالباركود
- نظام ائتمان العملاء
- تحسينات الواجهة
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout
from PyQt5.QtWidgets import QPushButton, QLabel, QStackedWidget, QMessageBox, QSplashScreen
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal
from PyQt5.QtGui import QFont, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>p, Q<PERSON><PERSON><PERSON>, QColor

# استيراد الوحدات المخصصة
from database.db_manager import DatabaseManager
from ui.login_window import LoginWindow
from ui.modern_dashboard import ModernDashboard
from utils.config import Config

class SplashScreen(QSplashScreen):
    """شاشة البداية المحسنة"""
    
    def __init__(self):
        # إنشاء صورة للشاشة
        pixmap = QPixmap(400, 300)
        pixmap.fill(QColor("#2c3e50"))
        
        # رسم المحتوى
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # الخلفية المتدرجة
        from PyQt5.QtGui import QLinearGradient
        gradient = QLinearGradient(0, 0, 400, 300)
        gradient.setColorAt(0, QColor("#74b9ff"))
        gradient.setColorAt(1, QColor("#0984e3"))
        painter.fillRect(pixmap.rect(), gradient)
        
        # النص الرئيسي
        painter.setPen(QColor("white"))
        painter.setFont(QFont("Arial", 24, QFont.Bold))
        painter.drawText(pixmap.rect(), Qt.AlignCenter, "نظام الطيب للتجارة\nوالتوزيع")
        
        # النص الفرعي
        painter.setFont(QFont("Arial", 12))
        painter.drawText(20, 250, "الإصدار المحسن - تحميل الميزات الجديدة...")
        
        painter.end()
        
        super().__init__(pixmap)
        self.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.FramelessWindowHint)
        
    def showMessage(self, message, color=Qt.white):
        """عرض رسالة على الشاشة"""
        super().showMessage(message, Qt.AlignBottom | Qt.AlignCenter, color)

class InitializationThread(QThread):
    """خيط تهيئة النظام"""
    
    progress_updated = pyqtSignal(str)
    finished_signal = pyqtSignal(bool, str)
    
    def run(self):
        """تشغيل التهيئة"""
        try:
            self.progress_updated.emit("تهيئة قاعدة البيانات...")
            QThread.msleep(500)
            
            # تهيئة قاعدة البيانات
            db_manager = DatabaseManager()
            
            self.progress_updated.emit("تحميل الإعدادات...")
            QThread.msleep(300)
            
            self.progress_updated.emit("فحص الميزات الجديدة...")
            QThread.msleep(400)
            
            # فحص الجداول الجديدة
            conn = db_manager.get_connection()
            cursor = conn.cursor()
            
            # فحص جدول الإشعارات
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='notifications'")
            if not cursor.fetchone():
                self.progress_updated.emit("إنشاء جدول الإشعارات...")
                cursor.execute('''
                    CREATE TABLE notifications (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        title TEXT NOT NULL,
                        message TEXT NOT NULL,
                        notification_type TEXT NOT NULL,
                        priority INTEGER DEFAULT 1,
                        is_read BOOLEAN DEFAULT 0,
                        user_id INTEGER,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES users (id)
                    )
                ''')
                conn.commit()
            
            conn.close()
            
            self.progress_updated.emit("تحميل الواجهة...")
            QThread.msleep(300)
            
            self.progress_updated.emit("جاهز للتشغيل!")
            QThread.msleep(200)
            
            self.finished_signal.emit(True, "تم تحميل النظام بنجاح")
            
        except Exception as e:
            self.finished_signal.emit(False, f"خطأ في التهيئة: {str(e)}")

class EnhancedPOSApplication(QMainWindow):
    """التطبيق المحسن لنظام نقاط البيع"""
    
    def __init__(self):
        super().__init__()
        self.db_manager = None
        self.current_user = None
        self.splash = None
        self.init_splash()
        
    def init_splash(self):
        """تهيئة شاشة البداية"""
        self.splash = SplashScreen()
        self.splash.show()
        
        # بدء التهيئة
        self.init_thread = InitializationThread()
        self.init_thread.progress_updated.connect(self.splash.showMessage)
        self.init_thread.finished_signal.connect(self.on_initialization_finished)
        self.init_thread.start()
        
    def on_initialization_finished(self, success, message):
        """عند انتهاء التهيئة"""
        if success:
            self.splash.showMessage("تم التحميل بنجاح! ✅")
            QTimer.singleShot(1000, self.show_login)
        else:
            self.splash.showMessage(f"خطأ: {message}")
            QTimer.singleShot(3000, self.close)
    
    def show_login(self):
        """عرض نافذة تسجيل الدخول"""
        self.splash.close()
        self.init_ui()
        self.show()
        
    def init_ui(self):
        """تهيئة واجهة المستخدم الرئيسية"""
        self.setWindowTitle("نظام نقاط البيع والمحاسبة - الإصدار المحسن")
        self.setGeometry(100, 100, 1200, 800)
        
        # إعداد الخط العربي
        font = QFont("Arial", 12)
        self.setFont(font)
        
        # تهيئة قاعدة البيانات
        self.db_manager = DatabaseManager()
        
        # إنشاء الواجهة المكدسة
        self.stacked_widget = QStackedWidget()
        self.setCentralWidget(self.stacked_widget)
        
        # إنشاء نافذة تسجيل الدخول
        self.login_window = LoginWindow(self.db_manager)
        self.login_window.login_successful.connect(self.on_login_success)
        self.stacked_widget.addWidget(self.login_window)
        
        # عرض نافذة تسجيل الدخول
        self.stacked_widget.setCurrentWidget(self.login_window)
        
        # إضافة شريط الحالة
        self.statusBar().showMessage("مرحباً بك في النظام المحسن - جاهز للاستخدام")
        
    def on_login_success(self, user_data):
        """معالج نجاح تسجيل الدخول"""
        self.current_user = user_data
        
        # إنشاء لوحة التحكم الحديثة
        self.main_dashboard = ModernDashboard(self.db_manager, self.current_user)
        self.main_dashboard.logout_requested.connect(self.on_logout)
        self.stacked_widget.addWidget(self.main_dashboard)
        
        # الانتقال للوحة التحكم
        self.stacked_widget.setCurrentWidget(self.main_dashboard)
        
        # تحديث شريط الحالة
        self.statusBar().showMessage(f"مرحباً {user_data.get('full_name', 'المستخدم')} - النظام جاهز")
        
        # إضافة إشعار ترحيب
        self.add_welcome_notification(user_data)
        
    def add_welcome_notification(self, user_data):
        """إضافة إشعار ترحيب"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            welcome_message = f"مرحباً {user_data.get('full_name', 'المستخدم')}! تم تسجيل الدخول بنجاح في النظام المحسن."
            
            cursor.execute('''
                INSERT INTO notifications (title, message, notification_type, priority, user_id)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                "مرحباً بك",
                welcome_message,
                "success",
                1,
                user_data.get('id')
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"خطأ في إضافة إشعار الترحيب: {str(e)}")
        
    def on_logout(self):
        """معالج تسجيل الخروج"""
        self.current_user = None
        self.stacked_widget.removeWidget(self.main_dashboard)
        self.stacked_widget.setCurrentWidget(self.login_window)
        self.login_window.clear_fields()
        self.statusBar().showMessage("تم تسجيل الخروج - مرحباً بك مرة أخرى")

def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    app = QApplication(sys.argv)
    
    # إعداد الترميز للعربية
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إعداد معلومات التطبيق
    app.setApplicationName("نظام الطيب للتجارة والتوزيع")
    app.setApplicationVersion("2.0 - الإصدار المحسن")
    app.setOrganizationName("الطيب للتجارة والتوزيع")
    
    # تطبيق نمط عام
    app.setStyleSheet("""
        QMainWindow {
            background-color: #f8f9fa;
        }
        QMessageBox {
            background-color: white;
            font-family: Arial;
        }
        QStatusBar {
            background-color: #e9ecef;
            color: #495057;
            border-top: 1px solid #dee2e6;
        }
    """)
    
    # إنشاء التطبيق الرئيسي
    pos_app = EnhancedPOSApplication()
    
    # معالج الإغلاق
    def on_exit():
        print("🔄 إغلاق النظام...")
        app.quit()
    
    app.aboutToQuit.connect(on_exit)
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    print("=" * 60)
    print("🚀 تشغيل نظام الطيب للتجارة والتوزيع - الإصدار المحسن")
    print("=" * 60)
    print("📋 الميزات الجديدة:")
    print("   ✅ نظام الإشعارات المتقدم")
    print("   ✅ التقارير المتقدمة والتحليلات")
    print("   ✅ البحث المحسن بالباركود")
    print("   ✅ نظام ائتمان العملاء")
    print("   ✅ تحسينات الواجهة والأداء")
    print("   ✅ نظام النسخ الاحتياطي المحسن")
    print("=" * 60)
    
    main()
