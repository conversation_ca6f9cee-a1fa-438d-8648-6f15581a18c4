#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة البحث بالباركود المحسنة - نظام نقاط البيع والمحاسبة
Enhanced Barcode Search Widget - POS and Accounting System
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QPushButton, QLineEdit, QFrame, QMessageBox,
                            QCompleter, QListWidget, QListWidgetItem)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QColor, QPalette
import sqlite3

class BarcodeSearchWidget(QWidget):
    """أداة البحث بالباركود المحسنة"""
    
    # إشارات
    product_selected = pyqtSignal(dict)  # عند اختيار منتج
    search_cleared = pyqtSignal()        # عند مسح البحث
    
    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.search_timer = QTimer()
        self.search_timer.setSingleShot(True)
        self.search_timer.timeout.connect(self.perform_search)
        self.init_ui()
        
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        
        # إطار البحث
        search_frame = QFrame()
        search_frame.setStyleSheet("""
            QFrame {
                background: #f8f9fa;
                border: 2px solid #e9ecef;
                border-radius: 10px;
                padding: 5px;
            }
        """)
        
        search_layout = QHBoxLayout(search_frame)
        
        # أيقونة البحث
        search_icon = QLabel("🔍")
        search_icon.setFont(QFont("Arial", 14))
        search_icon.setStyleSheet("color: #6c757d; padding: 5px;")
        search_layout.addWidget(search_icon)
        
        # حقل البحث
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("ابحث بالاسم أو الباركود...")
        self.search_input.setStyleSheet("""
            QLineEdit {
                border: none;
                background: transparent;
                font-size: 14px;
                padding: 8px;
                color: #495057;
            }
            QLineEdit:focus {
                outline: none;
            }
        """)
        
        # ربط الأحداث
        self.search_input.textChanged.connect(self.on_text_changed)
        self.search_input.returnPressed.connect(self.on_enter_pressed)
        
        search_layout.addWidget(self.search_input)
        
        # زر المسح
        self.clear_btn = QPushButton("✕")
        self.clear_btn.setFixedSize(30, 30)
        self.clear_btn.clicked.connect(self.clear_search)
        self.clear_btn.setStyleSheet("""
            QPushButton {
                background: #dc3545;
                color: white;
                border: none;
                border-radius: 15px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #c82333;
            }
        """)
        self.clear_btn.hide()  # مخفي في البداية
        search_layout.addWidget(self.clear_btn)
        
        layout.addWidget(search_frame)
        
        # قائمة النتائج
        self.results_list = QListWidget()
        self.results_list.setStyleSheet("""
            QListWidget {
                border: 1px solid #dee2e6;
                border-radius: 8px;
                background: white;
                alternate-background-color: #f8f9fa;
            }
            QListWidget::item {
                padding: 10px;
                border-bottom: 1px solid #e9ecef;
            }
            QListWidget::item:hover {
                background: #e3f2fd;
            }
            QListWidget::item:selected {
                background: #2196f3;
                color: white;
            }
        """)
        
        self.results_list.setAlternatingRowColors(True)
        self.results_list.itemClicked.connect(self.on_item_selected)
        self.results_list.hide()  # مخفية في البداية
        
        layout.addWidget(self.results_list)
        
        # رسالة عدم وجود نتائج
        self.no_results_label = QLabel("لا توجد نتائج")
        self.no_results_label.setAlignment(Qt.AlignCenter)
        self.no_results_label.setStyleSheet("""
            QLabel {
                color: #6c757d;
                font-style: italic;
                padding: 20px;
                background: #f8f9fa;
                border: 1px dashed #dee2e6;
                border-radius: 8px;
            }
        """)
        self.no_results_label.hide()
        
        layout.addWidget(self.no_results_label)
        
        self.setLayout(layout)
    
    def on_text_changed(self, text):
        """عند تغيير النص"""
        if text.strip():
            self.clear_btn.show()
            # تأخير البحث لتحسين الأداء
            self.search_timer.start(300)  # 300 مللي ثانية
        else:
            self.clear_btn.hide()
            self.hide_results()
            self.search_cleared.emit()
    
    def on_enter_pressed(self):
        """عند الضغط على Enter"""
        self.search_timer.stop()
        self.perform_search()
    
    def perform_search(self):
        """تنفيذ البحث"""
        search_text = self.search_input.text().strip()
        
        if not search_text:
            self.hide_results()
            return
        
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            # البحث في الاسم والباركود
            cursor.execute('''
                SELECT id, name, barcode, current_stock, retail_price, purchase_price
                FROM products
                WHERE name LIKE ? OR barcode LIKE ?
                ORDER BY 
                    CASE 
                        WHEN barcode = ? THEN 1
                        WHEN name = ? THEN 2
                        WHEN barcode LIKE ? THEN 3
                        WHEN name LIKE ? THEN 4
                        ELSE 5
                    END
                LIMIT 10
            ''', (
                f'%{search_text}%', f'%{search_text}%',
                search_text, search_text,
                f'{search_text}%', f'{search_text}%'
            ))
            
            results = cursor.fetchall()
            conn.close()
            
            self.display_results(results)
            
        except Exception as e:
            print(f"خطأ في البحث: {str(e)}")
            self.hide_results()
    
    def display_results(self, results):
        """عرض النتائج"""
        self.results_list.clear()
        
        if not results:
            self.show_no_results()
            return
        
        self.no_results_label.hide()
        self.results_list.show()
        
        for result in results:
            item_widget = self.create_result_item(result)
            item = QListWidgetItem()
            item.setSizeHint(item_widget.sizeHint())
            
            # حفظ بيانات المنتج
            product_data = {
                'id': result['id'],
                'name': result['name'],
                'barcode': result['barcode'],
                'current_stock': result['current_stock'],
                'retail_price': result['retail_price'],
                'purchase_price': result['purchase_price']
            }
            item.setData(Qt.UserRole, product_data)
            
            self.results_list.addItem(item)
            self.results_list.setItemWidget(item, item_widget)
    
    def create_result_item(self, result):
        """إنشاء عنصر نتيجة"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(10, 5, 10, 5)
        
        # معلومات المنتج
        info_layout = QVBoxLayout()
        
        # اسم المنتج
        name_label = QLabel(result['name'])
        name_label.setFont(QFont("Arial", 12, QFont.Bold))
        name_label.setStyleSheet("color: #212529;")
        info_layout.addWidget(name_label)
        
        # الباركود والمخزون
        details_layout = QHBoxLayout()
        
        if result['barcode']:
            barcode_label = QLabel(f"🏷️ {result['barcode']}")
            barcode_label.setStyleSheet("color: #6c757d; font-size: 11px;")
            details_layout.addWidget(barcode_label)
        
        stock_label = QLabel(f"📦 {result['current_stock']} قطعة")
        stock_color = "#28a745" if result['current_stock'] > 0 else "#dc3545"
        stock_label.setStyleSheet(f"color: {stock_color}; font-size: 11px; font-weight: bold;")
        details_layout.addWidget(stock_label)
        
        details_layout.addStretch()
        info_layout.addLayout(details_layout)
        
        layout.addLayout(info_layout)
        
        # السعر
        price_label = QLabel(f"{result['retail_price']:.2f} ج.م")
        price_label.setFont(QFont("Arial", 12, QFont.Bold))
        price_label.setStyleSheet("color: #007bff;")
        price_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        layout.addWidget(price_label)
        
        return widget
    
    def show_no_results(self):
        """عرض رسالة عدم وجود نتائج"""
        self.results_list.hide()
        self.no_results_label.show()
    
    def hide_results(self):
        """إخفاء النتائج"""
        self.results_list.hide()
        self.no_results_label.hide()
    
    def on_item_selected(self, item):
        """عند اختيار عنصر"""
        product_data = item.data(Qt.UserRole)
        if product_data:
            self.product_selected.emit(product_data)
            self.clear_search()
    
    def clear_search(self):
        """مسح البحث"""
        self.search_input.clear()
        self.hide_results()
        self.clear_btn.hide()
        self.search_cleared.emit()
    
    def focus_search(self):
        """تركيز على حقل البحث"""
        self.search_input.setFocus()
    
    def set_search_text(self, text):
        """تعيين نص البحث"""
        self.search_input.setText(text)
        if text.strip():
            self.perform_search()


class QuickBarcodeDialog(QWidget):
    """نافذة البحث السريع بالباركود"""
    
    product_selected = pyqtSignal(dict)
    
    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.setWindowTitle("البحث السريع بالباركود")
        self.setFixedSize(500, 400)
        self.init_ui()
        
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout()
        
        # العنوان
        title = QLabel("🔍 البحث السريع بالباركود")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 20px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #74b9ff, stop:1 #0984e3);
                color: white;
                border-radius: 10px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title)
        
        # أداة البحث
        self.search_widget = BarcodeSearchWidget(self.db_manager)
        self.search_widget.product_selected.connect(self.on_product_selected)
        layout.addWidget(self.search_widget)
        
        # تعليمات
        instructions = QLabel("💡 اكتب اسم المنتج أو امسح الباركود للبحث السريع")
        instructions.setAlignment(Qt.AlignCenter)
        instructions.setStyleSheet("""
            QLabel {
                color: #6c757d;
                font-style: italic;
                padding: 10px;
                background: #f8f9fa;
                border-radius: 5px;
                margin-top: 10px;
            }
        """)
        layout.addWidget(instructions)
        
        self.setLayout(layout)
        
        # تركيز على البحث
        self.search_widget.focus_search()
    
    def on_product_selected(self, product_data):
        """عند اختيار منتج"""
        self.product_selected.emit(product_data)
        self.close()
    
    def showEvent(self, event):
        """عند عرض النافذة"""
        super().showEvent(event)
        self.search_widget.focus_search()
