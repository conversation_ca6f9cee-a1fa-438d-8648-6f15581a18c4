#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة إشعارات تجريبية للنظام
Add Sample Notifications to System
"""

import sqlite3
from datetime import datetime, timedelta

def add_sample_notifications():
    """إضافة إشعارات تجريبية"""
    
    db_path = "pos_database.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # إشعارات تجريبية
        sample_notifications = [
            {
                'title': 'مرحباً بك في النظام المحدث',
                'message': 'تم إضافة ميزات جديدة رائعة للنظام! استكشف التقارير المتقدمة ونظام الإشعارات الجديد.',
                'type': 'success',
                'priority': 2
            },
            {
                'title': 'تنبيه مخزون منخفض',
                'message': 'يوجد 5 منتجات في المخزون أقل من الحد الأدنى المطلوب. يرجى إعادة التموين.',
                'type': 'warning',
                'priority': 3
            },
            {
                'title': 'تحديث النظام',
                'message': 'تم تحديث النظام بنجاح وإضافة الميزات التالية: نظام الإشعارات، التقارير المتقدمة، تحسين الباركود.',
                'type': 'info',
                'priority': 1
            },
            {
                'title': 'فاتورة جديدة',
                'message': 'تم إنشاء فاتورة بيع جديدة رقم S-2025-001 بقيمة 1,250.00 جنيه.',
                'type': 'success',
                'priority': 2
            },
            {
                'title': 'عميل جديد',
                'message': 'تم إضافة عميل جديد: أحمد محمد - رقم الهاتف: 01234567890',
                'type': 'info',
                'priority': 1
            },
            {
                'title': 'تحذير أمان',
                'message': 'تم محاولة دخول غير مصرح بها للنظام. يرجى التحقق من كلمات المرور.',
                'type': 'error',
                'priority': 3
            },
            {
                'title': 'نسخة احتياطية',
                'message': 'تم إنشاء نسخة احتياطية من البيانات بنجاح. آخر نسخة: ' + datetime.now().strftime('%Y-%m-%d %H:%M'),
                'type': 'success',
                'priority': 1
            },
            {
                'title': 'تقرير يومي',
                'message': f'إجمالي مبيعات اليوم: 3,450.00 جنيه | عدد الفواتير: 12 | متوسط الفاتورة: 287.50 جنيه',
                'type': 'info',
                'priority': 2
            },
            {
                'title': 'منتج جديد',
                'message': 'تم إضافة منتج جديد للمخزون: لابتوب HP - الكمية: 10 قطع - السعر: 15,000 جنيه',
                'type': 'success',
                'priority': 1
            },
            {
                'title': 'موعد صيانة',
                'message': 'موعد الصيانة الدورية للنظام مقرر خلال أسبوع. سيتم إشعارك قبل الموعد.',
                'type': 'warning',
                'priority': 2
            }
        ]
        
        # إضافة الإشعارات
        for i, notification in enumerate(sample_notifications):
            # تنويع التواريخ
            created_time = datetime.now() - timedelta(hours=i*2, minutes=i*15)
            
            cursor.execute('''
                INSERT INTO notifications (title, message, notification_type, priority, created_at)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                notification['title'],
                notification['message'],
                notification['type'],
                notification['priority'],
                created_time.strftime('%Y-%m-%d %H:%M:%S')
            ))
        
        conn.commit()
        conn.close()
        
        print("✅ تم إضافة الإشعارات التجريبية بنجاح!")
        print(f"📊 تم إضافة {len(sample_notifications)} إشعار")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة الإشعارات: {str(e)}")
        return False

def add_sample_data():
    """إضافة بيانات تجريبية إضافية"""
    
    db_path = "pos_database.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # إضافة عملاء تجريبيين
        sample_customers = [
            ('أحمد محمد علي', '01234567890', '<EMAIL>', 'القاهرة', 'retail'),
            ('فاطمة أحمد', '01987654321', '<EMAIL>', 'الجيزة', 'wholesale'),
            ('محمد حسن', '01555666777', '<EMAIL>', 'الإسكندرية', 'semi_wholesale'),
            ('سارة محمود', '01444555666', '<EMAIL>', 'المنصورة', 'retail'),
            ('عمر خالد', '01333444555', '<EMAIL>', 'أسوان', 'wholesale')
        ]
        
        for name, phone, email, address, customer_type in sample_customers:
            cursor.execute('''
                INSERT OR IGNORE INTO customers (name, phone, email, address, customer_type)
                VALUES (?, ?, ?, ?, ?)
            ''', (name, phone, email, address, customer_type))
        
        # إضافة منتجات تجريبية
        sample_products = [
            ('لابتوب HP', 'HP001', 'لابتوب HP Core i5', 'قطعة', 12000, 13000, 14000, 15000, 5, 2),
            ('ماوس لاسلكي', 'MS001', 'ماوس لاسلكي عالي الجودة', 'قطعة', 150, 180, 200, 250, 50, 10),
            ('كيبورد ميكانيكي', 'KB001', 'كيبورد ميكانيكي للألعاب', 'قطعة', 800, 900, 1000, 1200, 20, 5),
            ('شاشة 24 بوصة', 'MON001', 'شاشة LED 24 بوصة', 'قطعة', 2500, 2800, 3000, 3500, 8, 3),
            ('سماعات بلوتوث', 'HP001', 'سماعات بلوتوث عالية الجودة', 'قطعة', 300, 350, 400, 500, 30, 8)
        ]
        
        for name, barcode, desc, unit, purchase, wholesale, semi, retail, stock, min_stock in sample_products:
            cursor.execute('''
                INSERT OR IGNORE INTO products 
                (name, barcode, description, unit, purchase_price, wholesale_price, 
                 semi_wholesale_price, retail_price, current_stock, min_stock_alert)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (name, barcode, desc, unit, purchase, wholesale, semi, retail, stock, min_stock))
        
        conn.commit()
        conn.close()
        
        print("✅ تم إضافة البيانات التجريبية بنجاح!")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة البيانات التجريبية: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🔧 إضافة البيانات والإشعارات التجريبية")
    print("=" * 60)
    
    print("\n📊 إضافة الإشعارات التجريبية...")
    if add_sample_notifications():
        print("✅ تم إضافة الإشعارات بنجاح")
    else:
        print("❌ فشل في إضافة الإشعارات")
    
    print("\n📦 إضافة البيانات التجريبية...")
    if add_sample_data():
        print("✅ تم إضافة البيانات بنجاح")
    else:
        print("❌ فشل في إضافة البيانات")
    
    print("\n" + "=" * 60)
    print("🎉 تم الانتهاء من إضافة البيانات التجريبية!")
    print("📋 يمكنك الآن:")
    print("   1. تشغيل التطبيق")
    print("   2. فتح نافذة الإشعارات")
    print("   3. استكشاف التقارير المتقدمة")
    print("   4. تجربة البحث بالباركود")
    print("=" * 60)

if __name__ == "__main__":
    main()
