#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة التقارير المتقدمة - نظام نقاط البيع والمحاسبة
Advanced Reports Window - POS and Accounting System
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QPushButton, QTableWidget, QTableWidgetItem,
                            QHeaderView, QAbstractItemView, QFrame,
                            QMessageBox, QTabWidget, QDateEdit, QComboBox,
                            QGroupBox, QFormLayout, QSpinBox, QTextEdit,
                            QProgressBar, QSplitter, QTreeWidget, QTreeWidgetItem)
from PyQt5.QtCore import Qt, pyqtSignal, QDate, QThread
from PyQt5.QtGui import QFont, QColor, QPalette
from datetime import datetime, timedelta
import sqlite3

class AdvancedReportsWindow(QWidget):
    """نافذة التقارير المتقدمة"""
    
    def __init__(self, db_manager, user_data):
        super().__init__()
        self.db_manager = db_manager
        self.user_data = user_data
        self.init_ui()
        
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("التقارير المتقدمة")
        self.setGeometry(100, 100, 1200, 800)
        
        layout = QVBoxLayout()
        
        # شريط العنوان
        title_frame = QFrame()
        title_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #8e44ad, stop:1 #9b59b6);
                border-radius: 10px;
                margin: 5px;
            }
        """)
        title_layout = QHBoxLayout(title_frame)
        
        title_label = QLabel("📊 التقارير المتقدمة")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setStyleSheet("color: white; padding: 10px;")
        title_layout.addWidget(title_label)
        
        title_layout.addStretch()
        
        # زر التصدير
        self.export_btn = QPushButton("📤 تصدير")
        self.export_btn.clicked.connect(self.export_report)
        self.export_btn.setStyleSheet("""
            QPushButton {
                background: rgba(255,255,255,0.2);
                color: white;
                border: 1px solid rgba(255,255,255,0.3);
                border-radius: 5px;
                padding: 8px 15px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: rgba(255,255,255,0.3);
            }
        """)
        title_layout.addWidget(self.export_btn)
        
        layout.addWidget(title_frame)
        
        # التبويبات
        self.tabs = QTabWidget()
        
        # تبويب تقارير المبيعات
        self.sales_tab = self.create_sales_reports_tab()
        self.tabs.addTab(self.sales_tab, "📈 تقارير المبيعات")
        
        # تبويب تقارير المخزون
        self.inventory_tab = self.create_inventory_reports_tab()
        self.tabs.addTab(self.inventory_tab, "📦 تقارير المخزون")
        
        # تبويب تقارير العملاء
        self.customers_tab = self.create_customers_reports_tab()
        self.tabs.addTab(self.customers_tab, "👥 تقارير العملاء")
        
        # تبويب التقارير المالية
        self.financial_tab = self.create_financial_reports_tab()
        self.tabs.addTab(self.financial_tab, "💰 التقارير المالية")
        
        # تبويب التحليلات
        self.analytics_tab = self.create_analytics_tab()
        self.tabs.addTab(self.analytics_tab, "📊 التحليلات")
        
        layout.addWidget(self.tabs)
        
        self.setLayout(layout)
    
    def create_sales_reports_tab(self):
        """إنشاء تبويب تقارير المبيعات"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # فلاتر التقرير
        filters_frame = QGroupBox("فلاتر التقرير")
        filters_layout = QFormLayout()
        
        self.sales_from_date = QDateEdit()
        self.sales_from_date.setDate(QDate.currentDate().addDays(-30))
        self.sales_from_date.setCalendarPopup(True)
        filters_layout.addRow("من تاريخ:", self.sales_from_date)
        
        self.sales_to_date = QDateEdit()
        self.sales_to_date.setDate(QDate.currentDate())
        self.sales_to_date.setCalendarPopup(True)
        filters_layout.addRow("إلى تاريخ:", self.sales_to_date)
        
        self.sales_customer_combo = QComboBox()
        self.sales_customer_combo.addItem("جميع العملاء", None)
        self.load_customers_combo()
        filters_layout.addRow("العميل:", self.sales_customer_combo)
        
        self.sales_product_combo = QComboBox()
        self.sales_product_combo.addItem("جميع المنتجات", None)
        self.load_products_combo()
        filters_layout.addRow("المنتج:", self.sales_product_combo)
        
        # زر التقرير
        self.generate_sales_btn = QPushButton("📊 إنشاء التقرير")
        self.generate_sales_btn.clicked.connect(self.generate_sales_report)
        self.generate_sales_btn.setStyleSheet("""
            QPushButton {
                background: #3498db;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #2980b9;
            }
        """)
        filters_layout.addRow("", self.generate_sales_btn)
        
        filters_frame.setLayout(filters_layout)
        layout.addWidget(filters_frame)
        
        # جدول النتائج
        self.sales_table = QTableWidget()
        self.sales_table.setAlternatingRowColors(True)
        layout.addWidget(self.sales_table)
        
        # إحصائيات سريعة
        stats_frame = QFrame()
        stats_frame.setStyleSheet("""
            QFrame {
                background: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                margin: 5px;
            }
        """)
        stats_layout = QHBoxLayout(stats_frame)
        
        self.sales_total_label = QLabel("إجمالي المبيعات: 0.00 جنيه")
        self.sales_count_label = QLabel("عدد الفواتير: 0")
        self.sales_avg_label = QLabel("متوسط الفاتورة: 0.00 جنيه")
        
        for label in [self.sales_total_label, self.sales_count_label, self.sales_avg_label]:
            label.setFont(QFont("Arial", 10, QFont.Bold))
            label.setStyleSheet("padding: 10px; color: #495057;")
            stats_layout.addWidget(label)
        
        layout.addWidget(stats_frame)
        
        return tab
    
    def create_inventory_reports_tab(self):
        """إنشاء تبويب تقارير المخزون"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # أزرار التقارير
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)
        
        self.stock_status_btn = QPushButton("📋 حالة المخزون")
        self.stock_status_btn.clicked.connect(self.generate_stock_status_report)
        
        self.low_stock_btn = QPushButton("⚠️ المنتجات المنخفضة")
        self.low_stock_btn.clicked.connect(self.generate_low_stock_report)
        
        self.stock_movement_btn = QPushButton("🔄 حركة المخزون")
        self.stock_movement_btn.clicked.connect(self.generate_stock_movement_report)
        
        for btn in [self.stock_status_btn, self.low_stock_btn, self.stock_movement_btn]:
            btn.setStyleSheet("""
                QPushButton {
                    background: #28a745;
                    color: white;
                    border: none;
                    border-radius: 5px;
                    padding: 10px 15px;
                    font-weight: bold;
                    margin: 5px;
                }
                QPushButton:hover {
                    background: #218838;
                }
            """)
            buttons_layout.addWidget(btn)
        
        buttons_layout.addStretch()
        layout.addWidget(buttons_frame)
        
        # جدول المخزون
        self.inventory_table = QTableWidget()
        self.inventory_table.setAlternatingRowColors(True)
        layout.addWidget(self.inventory_table)
        
        return tab
    
    def create_customers_reports_tab(self):
        """إنشاء تبويب تقارير العملاء"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # أزرار التقارير
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout(buttons_frame)
        
        self.top_customers_btn = QPushButton("🏆 أفضل العملاء")
        self.top_customers_btn.clicked.connect(self.generate_top_customers_report)
        
        self.customer_balance_btn = QPushButton("💳 أرصدة العملاء")
        self.customer_balance_btn.clicked.connect(self.generate_customer_balance_report)
        
        self.customer_activity_btn = QPushButton("📊 نشاط العملاء")
        self.customer_activity_btn.clicked.connect(self.generate_customer_activity_report)
        
        for btn in [self.top_customers_btn, self.customer_balance_btn, self.customer_activity_btn]:
            btn.setStyleSheet("""
                QPushButton {
                    background: #fd7e14;
                    color: white;
                    border: none;
                    border-radius: 5px;
                    padding: 10px 15px;
                    font-weight: bold;
                    margin: 5px;
                }
                QPushButton:hover {
                    background: #e8590c;
                }
            """)
            buttons_layout.addWidget(btn)
        
        buttons_layout.addStretch()
        layout.addWidget(buttons_frame)
        
        # جدول العملاء
        self.customers_table = QTableWidget()
        self.customers_table.setAlternatingRowColors(True)
        layout.addWidget(self.customers_table)
        
        return tab
    
    def create_financial_reports_tab(self):
        """إنشاء تبويب التقارير المالية"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # فلاتر التاريخ
        date_frame = QGroupBox("فترة التقرير")
        date_layout = QHBoxLayout()
        
        date_layout.addWidget(QLabel("من:"))
        self.financial_from_date = QDateEdit()
        self.financial_from_date.setDate(QDate.currentDate().addDays(-30))
        self.financial_from_date.setCalendarPopup(True)
        date_layout.addWidget(self.financial_from_date)
        
        date_layout.addWidget(QLabel("إلى:"))
        self.financial_to_date = QDateEdit()
        self.financial_to_date.setDate(QDate.currentDate())
        self.financial_to_date.setCalendarPopup(True)
        date_layout.addWidget(self.financial_to_date)
        
        self.generate_financial_btn = QPushButton("📊 إنشاء التقرير المالي")
        self.generate_financial_btn.clicked.connect(self.generate_financial_report)
        self.generate_financial_btn.setStyleSheet("""
            QPushButton {
                background: #6f42c1;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #5a32a3;
            }
        """)
        date_layout.addWidget(self.generate_financial_btn)
        
        date_layout.addStretch()
        date_frame.setLayout(date_layout)
        layout.addWidget(date_frame)
        
        # البطاقات المالية
        cards_frame = QFrame()
        cards_layout = QHBoxLayout(cards_frame)
        
        # بطاقة الإيرادات
        revenue_card = self.create_financial_card("💰 الإيرادات", "0.00 جنيه", "#28a745")
        cards_layout.addWidget(revenue_card)
        
        # بطاقة المصروفات
        expenses_card = self.create_financial_card("💸 المصروفات", "0.00 جنيه", "#dc3545")
        cards_layout.addWidget(expenses_card)
        
        # بطاقة الربح
        profit_card = self.create_financial_card("📈 صافي الربح", "0.00 جنيه", "#17a2b8")
        cards_layout.addWidget(profit_card)
        
        layout.addWidget(cards_frame)
        
        # جدول التفاصيل المالية
        self.financial_table = QTableWidget()
        self.financial_table.setAlternatingRowColors(True)
        layout.addWidget(self.financial_table)
        
        return tab
    
    def create_analytics_tab(self):
        """إنشاء تبويب التحليلات"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # شجرة التحليلات
        self.analytics_tree = QTreeWidget()
        self.analytics_tree.setHeaderLabels(["التحليل", "القيمة", "النسبة", "الملاحظات"])
        
        # إضافة عقد التحليل
        sales_node = QTreeWidgetItem(["📈 تحليل المبيعات", "", "", ""])
        inventory_node = QTreeWidgetItem(["📦 تحليل المخزون", "", "", ""])
        customers_node = QTreeWidgetItem(["👥 تحليل العملاء", "", "", ""])
        
        self.analytics_tree.addTopLevelItem(sales_node)
        self.analytics_tree.addTopLevelItem(inventory_node)
        self.analytics_tree.addTopLevelItem(customers_node)
        
        layout.addWidget(self.analytics_tree)
        
        # زر تحديث التحليلات
        self.refresh_analytics_btn = QPushButton("🔄 تحديث التحليلات")
        self.refresh_analytics_btn.clicked.connect(self.refresh_analytics)
        self.refresh_analytics_btn.setStyleSheet("""
            QPushButton {
                background: #20c997;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #1ba085;
            }
        """)
        layout.addWidget(self.refresh_analytics_btn)
        
        return tab
    
    def create_financial_card(self, title, value, color):
        """إنشاء بطاقة مالية"""
        card = QFrame()
        card.setStyleSheet(f"""
            QFrame {{
                background: {color};
                border-radius: 10px;
                margin: 5px;
            }}
        """)
        
        layout = QVBoxLayout(card)
        
        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 12, QFont.Bold))
        title_label.setStyleSheet("color: white; padding: 10px;")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        value_label = QLabel(value)
        value_label.setFont(QFont("Arial", 16, QFont.Bold))
        value_label.setStyleSheet("color: white; padding: 5px;")
        value_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(value_label)
        
        return card
    
    def load_customers_combo(self):
        """تحميل قائمة العملاء"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("SELECT id, name FROM customers ORDER BY name")
            customers = cursor.fetchall()
            
            for customer in customers:
                self.sales_customer_combo.addItem(customer['name'], customer['id'])
            
            conn.close()
            
        except Exception as e:
            print(f"خطأ في تحميل العملاء: {str(e)}")
    
    def load_products_combo(self):
        """تحميل قائمة المنتجات"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("SELECT id, name FROM products ORDER BY name")
            products = cursor.fetchall()
            
            for product in products:
                self.sales_product_combo.addItem(product['name'], product['id'])
            
            conn.close()
            
        except Exception as e:
            print(f"خطأ في تحميل المنتجات: {str(e)}")
    
    def generate_sales_report(self):
        """إنشاء تقرير المبيعات"""
        try:
            from_date = self.sales_from_date.date().toString("yyyy-MM-dd")
            to_date = self.sales_to_date.date().toString("yyyy-MM-dd")
            
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            # استعلام المبيعات
            query = '''
                SELECT 
                    i.invoice_number,
                    i.invoice_date,
                    c.name as customer_name,
                    i.total_amount,
                    i.payment_method,
                    i.payment_status
                FROM invoices i
                LEFT JOIN customers c ON i.customer_id = c.id
                WHERE i.invoice_date BETWEEN ? AND ?
                ORDER BY i.invoice_date DESC
            '''
            
            cursor.execute(query, (from_date, to_date))
            sales_data = cursor.fetchall()
            
            # تحديث الجدول
            self.sales_table.setRowCount(len(sales_data))
            self.sales_table.setColumnCount(6)
            self.sales_table.setHorizontalHeaderLabels([
                "رقم الفاتورة", "التاريخ", "العميل", "المبلغ", "طريقة الدفع", "حالة الدفع"
            ])
            
            total_amount = 0
            for row, sale in enumerate(sales_data):
                self.sales_table.setItem(row, 0, QTableWidgetItem(sale['invoice_number']))
                self.sales_table.setItem(row, 1, QTableWidgetItem(sale['invoice_date']))
                self.sales_table.setItem(row, 2, QTableWidgetItem(sale['customer_name'] or "عميل نقدي"))
                self.sales_table.setItem(row, 3, QTableWidgetItem(f"{sale['total_amount']:.2f}"))
                self.sales_table.setItem(row, 4, QTableWidgetItem(sale['payment_method']))
                self.sales_table.setItem(row, 5, QTableWidgetItem(sale['payment_status']))
                
                total_amount += sale['total_amount']
            
            # تحديث الإحصائيات
            count = len(sales_data)
            avg_amount = total_amount / count if count > 0 else 0
            
            self.sales_total_label.setText(f"إجمالي المبيعات: {total_amount:.2f} جنيه")
            self.sales_count_label.setText(f"عدد الفواتير: {count}")
            self.sales_avg_label.setText(f"متوسط الفاتورة: {avg_amount:.2f} جنيه")
            
            # تنسيق الجدول
            self.sales_table.resizeColumnsToContents()
            
            conn.close()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء تقرير المبيعات:\n{str(e)}")
    
    def generate_stock_status_report(self):
        """إنشاء تقرير حالة المخزون"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT 
                    p.name,
                    p.barcode,
                    p.current_stock,
                    p.min_stock_alert,
                    p.purchase_price,
                    p.retail_price,
                    (p.current_stock * p.purchase_price) as stock_value
                FROM products p
                ORDER BY p.name
            ''')
            
            products = cursor.fetchall()
            
            # تحديث الجدول
            self.inventory_table.setRowCount(len(products))
            self.inventory_table.setColumnCount(7)
            self.inventory_table.setHorizontalHeaderLabels([
                "اسم المنتج", "الباركود", "الكمية الحالية", "الحد الأدنى", 
                "سعر الشراء", "سعر البيع", "قيمة المخزون"
            ])
            
            total_value = 0
            for row, product in enumerate(products):
                self.inventory_table.setItem(row, 0, QTableWidgetItem(product['name']))
                self.inventory_table.setItem(row, 1, QTableWidgetItem(product['barcode'] or ""))
                
                # تلوين الكمية حسب المستوى
                stock_item = QTableWidgetItem(str(product['current_stock']))
                if product['current_stock'] <= product['min_stock_alert']:
                    stock_item.setBackground(QColor("#ffebee"))
                    stock_item.setForeground(QColor("#c62828"))
                self.inventory_table.setItem(row, 2, stock_item)
                
                self.inventory_table.setItem(row, 3, QTableWidgetItem(str(product['min_stock_alert'])))
                self.inventory_table.setItem(row, 4, QTableWidgetItem(f"{product['purchase_price']:.2f}"))
                self.inventory_table.setItem(row, 5, QTableWidgetItem(f"{product['retail_price']:.2f}"))
                self.inventory_table.setItem(row, 6, QTableWidgetItem(f"{product['stock_value']:.2f}"))
                
                total_value += product['stock_value']
            
            self.inventory_table.resizeColumnsToContents()
            
            conn.close()
            
            QMessageBox.information(self, "تقرير المخزون", 
                                  f"إجمالي قيمة المخزون: {total_value:.2f} جنيه")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء تقرير المخزون:\n{str(e)}")
    
    def generate_low_stock_report(self):
        """إنشاء تقرير المنتجات المنخفضة"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT 
                    p.name,
                    p.current_stock,
                    p.min_stock_alert,
                    (p.min_stock_alert - p.current_stock) as shortage
                FROM products p
                WHERE p.current_stock <= p.min_stock_alert
                ORDER BY shortage DESC
            ''')
            
            low_stock_products = cursor.fetchall()
            
            if not low_stock_products:
                QMessageBox.information(self, "تقرير المخزون المنخفض", 
                                      "لا توجد منتجات منخفضة في المخزون")
                return
            
            # تحديث الجدول
            self.inventory_table.setRowCount(len(low_stock_products))
            self.inventory_table.setColumnCount(4)
            self.inventory_table.setHorizontalHeaderLabels([
                "اسم المنتج", "الكمية الحالية", "الحد الأدنى", "النقص"
            ])
            
            for row, product in enumerate(low_stock_products):
                name_item = QTableWidgetItem(product['name'])
                name_item.setBackground(QColor("#ffebee"))
                self.inventory_table.setItem(row, 0, name_item)
                
                stock_item = QTableWidgetItem(str(product['current_stock']))
                stock_item.setForeground(QColor("#c62828"))
                self.inventory_table.setItem(row, 1, stock_item)
                
                self.inventory_table.setItem(row, 2, QTableWidgetItem(str(product['min_stock_alert'])))
                self.inventory_table.setItem(row, 3, QTableWidgetItem(str(product['shortage'])))
            
            self.inventory_table.resizeColumnsToContents()
            
            conn.close()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء تقرير المنتجات المنخفضة:\n{str(e)}")
    
    def generate_stock_movement_report(self):
        """إنشاء تقرير حركة المخزون"""
        # هذه الوظيفة تحتاج لتطوير إضافي
        QMessageBox.information(self, "قريباً", "تقرير حركة المخزون قيد التطوير")
    
    def generate_top_customers_report(self):
        """إنشاء تقرير أفضل العملاء"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT 
                    c.name,
                    COUNT(i.id) as invoice_count,
                    SUM(i.total_amount) as total_purchases,
                    AVG(i.total_amount) as avg_purchase
                FROM customers c
                LEFT JOIN invoices i ON c.id = i.customer_id
                GROUP BY c.id, c.name
                HAVING total_purchases > 0
                ORDER BY total_purchases DESC
                LIMIT 20
            ''')
            
            top_customers = cursor.fetchall()
            
            # تحديث الجدول
            self.customers_table.setRowCount(len(top_customers))
            self.customers_table.setColumnCount(4)
            self.customers_table.setHorizontalHeaderLabels([
                "اسم العميل", "عدد الفواتير", "إجمالي المشتريات", "متوسط الفاتورة"
            ])
            
            for row, customer in enumerate(top_customers):
                self.customers_table.setItem(row, 0, QTableWidgetItem(customer['name']))
                self.customers_table.setItem(row, 1, QTableWidgetItem(str(customer['invoice_count'])))
                self.customers_table.setItem(row, 2, QTableWidgetItem(f"{customer['total_purchases']:.2f}"))
                self.customers_table.setItem(row, 3, QTableWidgetItem(f"{customer['avg_purchase']:.2f}"))
            
            self.customers_table.resizeColumnsToContents()
            
            conn.close()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء تقرير أفضل العملاء:\n{str(e)}")
    
    def generate_customer_balance_report(self):
        """إنشاء تقرير أرصدة العملاء"""
        # هذه الوظيفة تحتاج لتطوير إضافي
        QMessageBox.information(self, "قريباً", "تقرير أرصدة العملاء قيد التطوير")
    
    def generate_customer_activity_report(self):
        """إنشاء تقرير نشاط العملاء"""
        # هذه الوظيفة تحتاج لتطوير إضافي
        QMessageBox.information(self, "قريباً", "تقرير نشاط العملاء قيد التطوير")
    
    def generate_financial_report(self):
        """إنشاء التقرير المالي"""
        # هذه الوظيفة تحتاج لتطوير إضافي
        QMessageBox.information(self, "قريباً", "التقرير المالي قيد التطوير")
    
    def refresh_analytics(self):
        """تحديث التحليلات"""
        # هذه الوظيفة تحتاج لتطوير إضافي
        QMessageBox.information(self, "قريباً", "التحليلات قيد التطوير")
    
    def export_report(self):
        """تصدير التقرير"""
        # هذه الوظيفة تحتاج لتطوير إضافي
        QMessageBox.information(self, "قريباً", "تصدير التقارير قيد التطوير")
