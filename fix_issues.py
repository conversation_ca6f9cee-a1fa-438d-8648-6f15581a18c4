#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة تشخيص وحل مشاكل نظام نقاط البيع
System Issues Diagnosis and Fix Tool
"""

import sys
import os
import sqlite3
from datetime import datetime

def check_python_version():
    """فحص إصدار Python"""
    print("🔍 فحص إصدار Python...")
    version = sys.version_info
    print(f"   إصدار Python: {version.major}.{version.minor}.{version.micro}")
    
    if version.major >= 3 and version.minor >= 7:
        print("   ✅ إصدار Python مناسب")
        return True
    else:
        print("   ❌ إصدار Python قديم - يتطلب Python 3.7+")
        return False

def check_required_modules():
    """فحص المكتبات المطلوبة"""
    print("\n🔍 فحص المكتبات المطلوبة...")
    
    required_modules = [
        ('PyQt5', 'PyQt5'),
        ('sqlite3', 'sqlite3'),
        ('hashlib', 'hashlib'),
        ('datetime', 'datetime'),
        ('uuid', 'uuid'),
        ('os', 'os'),
        ('sys', 'sys')
    ]
    
    missing_modules = []
    
    for module_name, import_name in required_modules:
        try:
            __import__(import_name)
            print(f"   ✅ {module_name}")
        except ImportError:
            print(f"   ❌ {module_name} - مفقود")
            missing_modules.append(module_name)
    
    return missing_modules

def check_database():
    """فحص قاعدة البيانات"""
    print("\n🔍 فحص قاعدة البيانات...")
    
    db_files = ['pos_database.db', 'business_system.db']
    found_db = None
    
    for db_file in db_files:
        if os.path.exists(db_file):
            print(f"   ✅ وجد ملف قاعدة البيانات: {db_file}")
            found_db = db_file
            break
    
    if not found_db:
        print("   ⚠️ لم يتم العثور على ملف قاعدة البيانات")
        return False
    
    # فحص هيكل قاعدة البيانات
    try:
        conn = sqlite3.connect(found_db)
        cursor = conn.cursor()
        
        # فحص الجداول الأساسية
        required_tables = ['users', 'products', 'customers', 'suppliers', 'invoices']
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        existing_tables = [row[0] for row in cursor.fetchall()]
        
        missing_tables = []
        for table in required_tables:
            if table in existing_tables:
                print(f"   ✅ جدول {table}")
            else:
                print(f"   ❌ جدول {table} - مفقود")
                missing_tables.append(table)
        
        conn.close()
        
        if missing_tables:
            print(f"   ⚠️ جداول مفقودة: {', '.join(missing_tables)}")
            return False
        else:
            print("   ✅ هيكل قاعدة البيانات سليم")
            return True
            
    except Exception as e:
        print(f"   ❌ خطأ في فحص قاعدة البيانات: {str(e)}")
        return False

def check_ui_files():
    """فحص ملفات واجهة المستخدم"""
    print("\n🔍 فحص ملفات واجهة المستخدم...")
    
    ui_files = [
        'ui/login_window.py',
        'ui/main_dashboard.py',
        'ui/modern_dashboard.py',
        'ui/products_window.py',
        'ui/sales_window.py',
        'ui/customers_window.py'
    ]
    
    missing_files = []
    
    for file_path in ui_files:
        if os.path.exists(file_path):
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path} - مفقود")
            missing_files.append(file_path)
    
    return missing_files

def fix_database():
    """إصلاح قاعدة البيانات"""
    print("\n🔧 إصلاح قاعدة البيانات...")
    
    try:
        from database.db_manager import DatabaseManager
        
        print("   🔄 إنشاء/تحديث قاعدة البيانات...")
        db_manager = DatabaseManager()
        print("   ✅ تم إصلاح قاعدة البيانات بنجاح")
        return True
        
    except Exception as e:
        print(f"   ❌ فشل في إصلاح قاعدة البيانات: {str(e)}")
        return False

def create_missing_files():
    """إنشاء الملفات المفقودة"""
    print("\n🔧 إنشاء الملفات المفقودة...")
    
    # إنشاء مجلد ui إذا لم يكن موجوداً
    if not os.path.exists('ui'):
        os.makedirs('ui')
        print("   ✅ تم إنشاء مجلد ui")
    
    # إنشاء ملف __init__.py في مجلد ui
    init_file = 'ui/__init__.py'
    if not os.path.exists(init_file):
        with open(init_file, 'w', encoding='utf-8') as f:
            f.write('# UI Package\n')
        print("   ✅ تم إنشاء ui/__init__.py")

def test_basic_functionality():
    """اختبار الوظائف الأساسية"""
    print("\n🧪 اختبار الوظائف الأساسية...")
    
    try:
        # اختبار استيراد المكتبات الأساسية
        from PyQt5.QtWidgets import QApplication
        from database.db_manager import DatabaseManager
        
        print("   ✅ استيراد المكتبات الأساسية")
        
        # اختبار قاعدة البيانات
        db_manager = DatabaseManager()
        user = db_manager.authenticate_user('admin', 'admin123')
        
        if user:
            print("   ✅ تسجيل الدخول يعمل")
        else:
            print("   ⚠️ مشكلة في تسجيل الدخول")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في الاختبار: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🔧 أداة تشخيص وحل مشاكل نظام نقاط البيع")
    print("=" * 60)
    
    # فحص النظام
    issues = []
    
    if not check_python_version():
        issues.append("إصدار Python")
    
    missing_modules = check_required_modules()
    if missing_modules:
        issues.append(f"مكتبات مفقودة: {', '.join(missing_modules)}")
    
    if not check_database():
        issues.append("قاعدة البيانات")
    
    missing_ui_files = check_ui_files()
    if missing_ui_files:
        issues.append(f"ملفات واجهة مفقودة: {', '.join(missing_ui_files)}")
    
    # عرض النتائج
    print("\n" + "=" * 60)
    if issues:
        print("❌ تم العثور على مشاكل:")
        for issue in issues:
            print(f"   • {issue}")
        
        print("\n🔧 محاولة الإصلاح...")
        
        # محاولة الإصلاح
        if "قاعدة البيانات" in str(issues):
            fix_database()
        
        create_missing_files()
        
    else:
        print("✅ لم يتم العثور على مشاكل!")
    
    # اختبار نهائي
    print("\n" + "=" * 60)
    if test_basic_functionality():
        print("🎉 النظام جاهز للعمل!")
        print("\n📋 طرق تشغيل التطبيق:")
        print("   1. python main.py")
        print("   2. python modern_app.py")
        print("   3. انقر مرتين على start_app.bat")
    else:
        print("⚠️ يحتاج النظام لمزيد من الإصلاح")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
