#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة الإشعارات - نظام نقاط البيع والمحاسبة
Notifications Window - POS and Accounting System
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QPushButton, QTableWidget, QTableWidgetItem,
                            QHeaderView, QAbstractItemView, QFrame,
                            QMessageBox, QMenu, QAction, QTextEdit,
                            QDialog, QFormLayout, QComboBox, QSpinBox)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QColor, QIcon
from datetime import datetime

class NotificationsWindow(QWidget):
    """نافذة الإشعارات"""
    
    def __init__(self, db_manager, user_data):
        super().__init__()
        self.db_manager = db_manager
        self.user_data = user_data
        self.init_ui()
        self.load_notifications()
        
        # تحديث الإشعارات كل دقيقة
        self.timer = QTimer()
        self.timer.timeout.connect(self.load_notifications)
        self.timer.start(60000)  # 60 ثانية
        
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("الإشعارات")
        self.setGeometry(100, 100, 800, 600)
        
        layout = QVBoxLayout()
        
        # شريط العنوان
        title_frame = QFrame()
        title_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498db, stop:1 #2980b9);
                border-radius: 10px;
                margin: 5px;
            }
        """)
        title_layout = QHBoxLayout(title_frame)
        
        title_label = QLabel("🔔 الإشعارات")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setStyleSheet("color: white; padding: 10px;")
        title_layout.addWidget(title_label)
        
        title_layout.addStretch()
        
        # أزرار التحكم
        self.refresh_btn = QPushButton("🔄 تحديث")
        self.refresh_btn.clicked.connect(self.load_notifications)
        self.refresh_btn.setStyleSheet("""
            QPushButton {
                background: rgba(255,255,255,0.2);
                color: white;
                border: 1px solid rgba(255,255,255,0.3);
                border-radius: 5px;
                padding: 8px 15px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: rgba(255,255,255,0.3);
            }
        """)
        title_layout.addWidget(self.refresh_btn)
        
        self.add_btn = QPushButton("➕ إضافة إشعار")
        self.add_btn.clicked.connect(self.add_notification)
        self.add_btn.setStyleSheet(self.refresh_btn.styleSheet())
        title_layout.addWidget(self.add_btn)
        
        layout.addWidget(title_frame)
        
        # إحصائيات سريعة
        stats_frame = QFrame()
        stats_frame.setStyleSheet("""
            QFrame {
                background: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                margin: 5px;
            }
        """)
        stats_layout = QHBoxLayout(stats_frame)
        
        self.total_label = QLabel("المجموع: 0")
        self.unread_label = QLabel("غير مقروءة: 0")
        self.high_priority_label = QLabel("عالية الأولوية: 0")
        
        for label in [self.total_label, self.unread_label, self.high_priority_label]:
            label.setFont(QFont("Arial", 10, QFont.Bold))
            label.setStyleSheet("padding: 10px; color: #495057;")
            stats_layout.addWidget(label)
        
        stats_layout.addStretch()
        layout.addWidget(stats_frame)
        
        # جدول الإشعارات
        self.notifications_table = QTableWidget()
        self.notifications_table.setColumnCount(6)
        self.notifications_table.setHorizontalHeaderLabels([
            "الأولوية", "النوع", "العنوان", "الرسالة", "التاريخ", "الحالة"
        ])
        
        # تنسيق الجدول
        self.notifications_table.horizontalHeader().setStretchLastSection(True)
        self.notifications_table.setAlternatingRowColors(True)
        self.notifications_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.notifications_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        
        # تعديل عرض الأعمدة
        header = self.notifications_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)
        header.setSectionResizeMode(1, QHeaderView.Fixed)
        header.setSectionResizeMode(2, QHeaderView.Stretch)
        header.setSectionResizeMode(3, QHeaderView.Stretch)
        header.setSectionResizeMode(4, QHeaderView.Fixed)
        header.setSectionResizeMode(5, QHeaderView.Fixed)
        
        self.notifications_table.setColumnWidth(0, 80)
        self.notifications_table.setColumnWidth(1, 100)
        self.notifications_table.setColumnWidth(4, 150)
        self.notifications_table.setColumnWidth(5, 100)
        
        # قائمة السياق
        self.notifications_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.notifications_table.customContextMenuRequested.connect(self.show_context_menu)
        
        # النقر المزدوج
        self.notifications_table.doubleClicked.connect(self.mark_as_read)
        
        layout.addWidget(self.notifications_table)
        
        # شريط الحالة
        status_frame = QFrame()
        status_frame.setStyleSheet("""
            QFrame {
                background: #e9ecef;
                border-top: 1px solid #dee2e6;
                padding: 5px;
            }
        """)
        status_layout = QHBoxLayout(status_frame)
        
        self.status_label = QLabel("جاهز")
        self.status_label.setStyleSheet("color: #6c757d; font-size: 12px;")
        status_layout.addWidget(self.status_label)
        
        status_layout.addStretch()
        
        # أزرار الإجراءات
        self.mark_read_btn = QPushButton("✓ تحديد كمقروء")
        self.mark_read_btn.clicked.connect(self.mark_as_read)
        self.mark_read_btn.setStyleSheet("""
            QPushButton {
                background: #28a745;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 15px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #218838;
            }
        """)
        status_layout.addWidget(self.mark_read_btn)
        
        self.delete_btn = QPushButton("🗑️ حذف")
        self.delete_btn.clicked.connect(self.delete_notification)
        self.delete_btn.setStyleSheet("""
            QPushButton {
                background: #dc3545;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 15px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #c82333;
            }
        """)
        status_layout.addWidget(self.delete_btn)
        
        layout.addWidget(status_frame)
        
        self.setLayout(layout)
        
    def load_notifications(self):
        """تحميل الإشعارات"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT id, title, message, notification_type, priority, is_read, created_at
                FROM notifications
                ORDER BY priority DESC, created_at DESC
            ''')
            
            notifications = cursor.fetchall()
            
            # تحديث الجدول
            self.notifications_table.setRowCount(len(notifications))
            
            for row, notification in enumerate(notifications):
                # الأولوية
                priority_text = {1: "منخفضة", 2: "متوسطة", 3: "عالية"}.get(notification['priority'], "غير محدد")
                priority_item = QTableWidgetItem(priority_text)
                
                # تلوين حسب الأولوية
                if notification['priority'] == 3:
                    priority_item.setBackground(QColor("#ffebee"))
                    priority_item.setForeground(QColor("#c62828"))
                elif notification['priority'] == 2:
                    priority_item.setBackground(QColor("#fff3e0"))
                    priority_item.setForeground(QColor("#ef6c00"))
                else:
                    priority_item.setBackground(QColor("#e8f5e8"))
                    priority_item.setForeground(QColor("#2e7d32"))
                
                self.notifications_table.setItem(row, 0, priority_item)
                
                # النوع
                type_text = {
                    'info': 'معلومات',
                    'warning': 'تحذير',
                    'error': 'خطأ',
                    'success': 'نجاح'
                }.get(notification['notification_type'], notification['notification_type'])
                
                type_item = QTableWidgetItem(type_text)
                self.notifications_table.setItem(row, 1, type_item)
                
                # العنوان
                title_item = QTableWidgetItem(notification['title'])
                if not notification['is_read']:
                    title_item.setFont(QFont("Arial", 10, QFont.Bold))
                self.notifications_table.setItem(row, 2, title_item)
                
                # الرسالة
                message_item = QTableWidgetItem(notification['message'][:100] + "..." if len(notification['message']) > 100 else notification['message'])
                self.notifications_table.setItem(row, 3, message_item)
                
                # التاريخ
                date_item = QTableWidgetItem(notification['created_at'][:16])
                self.notifications_table.setItem(row, 4, date_item)
                
                # الحالة
                status_text = "مقروء" if notification['is_read'] else "جديد"
                status_item = QTableWidgetItem(status_text)
                if not notification['is_read']:
                    status_item.setBackground(QColor("#e3f2fd"))
                    status_item.setForeground(QColor("#1976d2"))
                self.notifications_table.setItem(row, 5, status_item)
                
                # حفظ معرف الإشعار
                for col in range(6):
                    item = self.notifications_table.item(row, col)
                    if item:
                        item.setData(Qt.UserRole, notification['id'])
            
            # تحديث الإحصائيات
            total_count = len(notifications)
            unread_count = sum(1 for n in notifications if not n['is_read'])
            high_priority_count = sum(1 for n in notifications if n['priority'] == 3)
            
            self.total_label.setText(f"المجموع: {total_count}")
            self.unread_label.setText(f"غير مقروءة: {unread_count}")
            self.high_priority_label.setText(f"عالية الأولوية: {high_priority_count}")
            
            self.status_label.setText(f"تم تحديث الإشعارات - {datetime.now().strftime('%H:%M:%S')}")
            
            conn.close()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل الإشعارات:\n{str(e)}")
    
    def show_context_menu(self, position):
        """عرض قائمة السياق"""
        if self.notifications_table.itemAt(position):
            menu = QMenu()
            
            mark_read_action = QAction("تحديد كمقروء", self)
            mark_read_action.triggered.connect(self.mark_as_read)
            menu.addAction(mark_read_action)
            
            delete_action = QAction("حذف", self)
            delete_action.triggered.connect(self.delete_notification)
            menu.addAction(delete_action)
            
            menu.exec_(self.notifications_table.mapToGlobal(position))
    
    def mark_as_read(self):
        """تحديد الإشعار كمقروء"""
        current_row = self.notifications_table.currentRow()
        if current_row >= 0:
            item = self.notifications_table.item(current_row, 0)
            if item:
                notification_id = item.data(Qt.UserRole)
                
                try:
                    conn = self.db_manager.get_connection()
                    cursor = conn.cursor()
                    
                    cursor.execute('''
                        UPDATE notifications
                        SET is_read = 1
                        WHERE id = ?
                    ''', (notification_id,))
                    
                    conn.commit()
                    conn.close()
                    
                    self.load_notifications()
                    
                except Exception as e:
                    QMessageBox.critical(self, "خطأ", f"فشل في تحديث الإشعار:\n{str(e)}")
    
    def delete_notification(self):
        """حذف الإشعار"""
        current_row = self.notifications_table.currentRow()
        if current_row >= 0:
            reply = QMessageBox.question(self, "تأكيد الحذف", 
                                       "هل أنت متأكد من حذف هذا الإشعار؟",
                                       QMessageBox.Yes | QMessageBox.No)
            
            if reply == QMessageBox.Yes:
                item = self.notifications_table.item(current_row, 0)
                if item:
                    notification_id = item.data(Qt.UserRole)
                    
                    try:
                        conn = self.db_manager.get_connection()
                        cursor = conn.cursor()
                        
                        cursor.execute('DELETE FROM notifications WHERE id = ?', (notification_id,))
                        
                        conn.commit()
                        conn.close()
                        
                        self.load_notifications()
                        
                    except Exception as e:
                        QMessageBox.critical(self, "خطأ", f"فشل في حذف الإشعار:\n{str(e)}")
    
    def add_notification(self):
        """إضافة إشعار جديد"""
        dialog = AddNotificationDialog(self.db_manager, self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_notifications()


class AddNotificationDialog(QDialog):
    """نافذة إضافة إشعار جديد"""
    
    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.init_ui()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("إضافة إشعار جديد")
        self.setFixedSize(400, 300)
        
        layout = QVBoxLayout()
        
        # النموذج
        form_layout = QFormLayout()
        
        self.title_input = QLineEdit()
        self.title_input.setPlaceholderText("عنوان الإشعار")
        form_layout.addRow("العنوان:", self.title_input)
        
        self.message_input = QTextEdit()
        self.message_input.setPlaceholderText("نص الإشعار")
        self.message_input.setMaximumHeight(100)
        form_layout.addRow("الرسالة:", self.message_input)
        
        self.type_combo = QComboBox()
        self.type_combo.addItems(["معلومات", "تحذير", "خطأ", "نجاح"])
        form_layout.addRow("النوع:", self.type_combo)
        
        self.priority_spin = QSpinBox()
        self.priority_spin.setRange(1, 3)
        self.priority_spin.setValue(2)
        form_layout.addRow("الأولوية (1-3):", self.priority_spin)
        
        layout.addLayout(form_layout)
        
        # الأزرار
        buttons_layout = QHBoxLayout()
        
        self.save_btn = QPushButton("💾 حفظ")
        self.save_btn.clicked.connect(self.save_notification)
        self.save_btn.setStyleSheet("""
            QPushButton {
                background: #28a745;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #218838;
            }
        """)
        buttons_layout.addWidget(self.save_btn)
        
        self.cancel_btn = QPushButton("❌ إلغاء")
        self.cancel_btn.clicked.connect(self.reject)
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background: #6c757d;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #5a6268;
            }
        """)
        buttons_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(buttons_layout)
        
        self.setLayout(layout)
    
    def save_notification(self):
        """حفظ الإشعار"""
        title = self.title_input.text().strip()
        message = self.message_input.toPlainText().strip()
        
        if not title or not message:
            QMessageBox.warning(self, "تحذير", "يرجى ملء جميع الحقول المطلوبة")
            return
        
        type_map = {
            "معلومات": "info",
            "تحذير": "warning", 
            "خطأ": "error",
            "نجاح": "success"
        }
        
        notification_type = type_map[self.type_combo.currentText()]
        priority = self.priority_spin.value()
        
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO notifications (title, message, notification_type, priority)
                VALUES (?, ?, ?, ?)
            ''', (title, message, notification_type, priority))
            
            conn.commit()
            conn.close()
            
            QMessageBox.information(self, "نجح", "تم إضافة الإشعار بنجاح")
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إضافة الإشعار:\n{str(e)}")
