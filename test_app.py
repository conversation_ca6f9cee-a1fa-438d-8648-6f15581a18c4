#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع للتطبيق - نظام نقاط البيع والمحاسبة
Quick App Test - POS and Accounting System
"""

import sys
import os
from datetime import datetime

def test_imports():
    """اختبار الاستيرادات"""
    print("🔍 اختبار الاستيرادات...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        print("   ✅ PyQt5 - OK")
        
        from database.db_manager import DatabaseManager
        print("   ✅ DatabaseManager - OK")
        
        from ui.login_window import LoginWindow
        print("   ✅ LoginWindow - OK")
        
        from ui.modern_dashboard import ModernDashboard
        print("   ✅ ModernDashboard - OK")
        
        from ui.notifications_window import NotificationsWindow
        print("   ✅ NotificationsWindow - OK")
        
        from ui.advanced_reports_window import AdvancedReportsWindow
        print("   ✅ AdvancedReportsWindow - OK")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في الاستيراد: {str(e)}")
        return False

def test_database():
    """اختبار قاعدة البيانات"""
    print("\n🗄️ اختبار قاعدة البيانات...")
    
    try:
        from database.db_manager import DatabaseManager
        
        db_manager = DatabaseManager()
        print("   ✅ إنشاء DatabaseManager - OK")
        
        # اختبار الاتصال
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        # فحص الجداول الأساسية
        tables_to_check = ['users', 'products', 'customers', 'suppliers', 'invoices', 'notifications']
        
        for table in tables_to_check:
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
            if cursor.fetchone():
                print(f"   ✅ جدول {table} - موجود")
            else:
                print(f"   ⚠️ جدول {table} - مفقود")
        
        # اختبار تسجيل الدخول
        user = db_manager.authenticate_user('admin', 'admin123')
        if user:
            print("   ✅ تسجيل الدخول - يعمل")
        else:
            print("   ⚠️ تسجيل الدخول - مشكلة")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في قاعدة البيانات: {str(e)}")
        return False

def test_notifications():
    """اختبار نظام الإشعارات"""
    print("\n🔔 اختبار نظام الإشعارات...")
    
    try:
        from database.db_manager import DatabaseManager
        
        db_manager = DatabaseManager()
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        # عد الإشعارات
        cursor.execute("SELECT COUNT(*) FROM notifications")
        count = cursor.fetchone()[0]
        print(f"   📊 عدد الإشعارات: {count}")
        
        # عد الإشعارات غير المقروءة
        cursor.execute("SELECT COUNT(*) FROM notifications WHERE is_read = 0")
        unread_count = cursor.fetchone()[0]
        print(f"   📬 الإشعارات غير المقروءة: {unread_count}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في نظام الإشعارات: {str(e)}")
        return False

def test_products():
    """اختبار المنتجات"""
    print("\n📦 اختبار المنتجات...")
    
    try:
        from database.db_manager import DatabaseManager
        
        db_manager = DatabaseManager()
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        # عد المنتجات
        cursor.execute("SELECT COUNT(*) FROM products")
        count = cursor.fetchone()[0]
        print(f"   📊 عدد المنتجات: {count}")
        
        # المنتجات المنخفضة
        cursor.execute("SELECT COUNT(*) FROM products WHERE current_stock <= min_stock_alert")
        low_stock_count = cursor.fetchone()[0]
        print(f"   ⚠️ المنتجات المنخفضة: {low_stock_count}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في المنتجات: {str(e)}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 60)
    print("🧪 اختبار سريع لنظام نقاط البيع")
    print(f"⏰ الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 4
    
    # تشغيل الاختبارات
    if test_imports():
        tests_passed += 1
    
    if test_database():
        tests_passed += 1
    
    if test_notifications():
        tests_passed += 1
    
    if test_products():
        tests_passed += 1
    
    # النتائج
    print("\n" + "=" * 60)
    print("📊 نتائج الاختبار:")
    print(f"   ✅ نجح: {tests_passed}/{total_tests}")
    print(f"   ❌ فشل: {total_tests - tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        print("\n🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام")
        print("\n📋 طرق التشغيل:")
        print("   1. python main.py")
        print("   2. python modern_app.py")
        print("   3. انقر مرتين على start_app.bat")
        
        print("\n🔑 بيانات تسجيل الدخول:")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: admin123")
        
        print("\n🆕 الميزات الجديدة:")
        print("   🔔 الإشعارات")
        print("   📈 التقارير المتقدمة")
        print("   🔍 البحث المحسن")
        print("   💳 نظام الائتمان")
        
    else:
        print("\n⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
